#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/E:/HuaweiMoveData/Users/<USER>/Desktop/pmo(new)/pmo-web/node_modules/.store/rollup@4.44.2/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/E:/HuaweiMoveData/Users/<USER>/Desktop/pmo(new)/pmo-web/node_modules/.store/rollup@4.44.2/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../rollup@4.44.2/node_modules/rollup/dist/bin/rollup" "$@"
else
  exec node  "$basedir/../../../../../rollup@4.44.2/node_modules/rollup/dist/bin/rollup" "$@"
fi
